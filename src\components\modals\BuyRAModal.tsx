'use client'

import { useEffect, useState } from 'react'
import { X, ExternalLink } from 'lucide-react'
import Image from 'next/image'

interface Platform {
  name: string
  url: string
  imagePath: string
  description: string
}

interface BuyRAModalProps {
  isOpen: boolean
  onClose: () => void
}

const platforms: Platform[] = [
  {
    name: 'DEXTools',
    url: 'https://www.dextools.io',
    imagePath: '/buy/dextool.png',
    description: 'Advanced trading analytics'
  },
  {
    name: 'DEXScreener',
    url: 'https://dexscreener.com',
    imagePath: '/buy/dexscreener.png',
    description: 'Real-time DEX data'
  },
  {
    name: 'Meteora',
    url: 'https://meteora.ag',
    imagePath: '/buy/meteora.png',
    description: 'Dynamic liquidity protocol'
  },
  {
    name: 'Raydium',
    url: 'https://raydium.io',
    imagePath: '/buy/raydium.png',
    description: 'Automated market maker'
  }
]

export function BuyRAModal({ isOpen, onClose }: BuyRAModalProps) {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      // Prevent body scrolling
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  // Handle platform click
  const handlePlatformClick = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  // Handle image error
  const handleImageError = (platformName: string) => {
    setImageErrors(prev => new Set(prev).add(platformName))
  }

  // Handle backdrop click
  const handleBackdropClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black/60 backdrop-blur-sm" />

      {/* Modal */}
      <div
        className="relative w-full max-w-2xl max-h-[90vh] overflow-y-auto rounded bg-gray-900 border border-gray-700 shadow-2xl"
        style={{ background: 'var(--bg-primary)' }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-white">Buy RA Tokens</h2>
            <p className="text-sm text-gray-400 mt-1">Choose a platform to purchase RA tokens</p>
          </div>
          <button
            onClick={onClose}
            className="w-8 h-8 rounded bg-gray-800 hover:bg-gray-700 flex items-center justify-center transition-colors"
            aria-label="Close modal"
          >
            <X className="w-4 h-4 text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {platforms.map((platform) => (
              <button
                key={platform.name}
                onClick={() => handlePlatformClick(platform.url)}
                className="flex items-center p-4 rounded bg-gray-800/50 border border-gray-700 hover:bg-gray-700/50 hover:border-gray-600 transition-all duration-200 text-left group"
              >
                <div className="w-12 h-12 rounded bg-white/10 flex items-center justify-center mr-4 group-hover:bg-white/20 transition-colors">
                  {imageErrors.has(platform.name) ? (
                    <ExternalLink className="w-6 h-6 text-gray-400" />
                  ) : (
                    <Image
                      src={platform.imagePath}
                      alt={platform.name}
                      width={32}
                      height={32}
                      className="w-8 h-8 object-contain"
                      onError={() => handleImageError(platform.name)}
                    />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-white group-hover:text-blue-400 transition-colors">
                      {platform.name}
                    </h3>
                    <ExternalLink className="w-4 h-4 text-gray-500 group-hover:text-gray-400 transition-colors" />
                  </div>
                  <p className="text-sm text-gray-400 mt-1">
                    {platform.description}
                  </p>
                </div>
              </button>
            ))}
          </div>

          {/* Disclaimer */}
          <div className="mt-6 p-4 rounded bg-yellow-500/10 border border-yellow-500/20">
            <p className="text-sm text-yellow-200">
              <strong>Disclaimer:</strong> Always verify the authenticity of the platform before making any transactions.
              Ensure you're on the official website and never share your private keys or seed phrases.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
