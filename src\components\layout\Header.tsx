'use client'

import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useWallet, useConnection } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { useEffect, useState, useRef } from 'react'
import { PublicKey } from '@solana/web3.js'
import { getAssociatedTokenAddress, getAccount } from '@solana/spl-token'
import { User, Menu, X, ChevronDown } from 'lucide-react'

const navigation = [
  { name: 'Dashboard', href: '/' },
  { name: 'Assets', href: '/assets' },
  { name: 'Planning', href: '/planning' },
  { name: 'Docs', href: 'https://docs.solera.work', external: true },
]

// Solera RA token contract address
const SOLERA_RA_TOKEN_ADDRESS = '2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA'

export function Header() {
  const pathname = usePathname()
  const { connected, publicKey, disconnect } = useWallet()
  const { connection } = useConnection()
  const [tokenBalance, setTokenBalance] = useState<number | null>(null)
  const [isLoadingBalance, setIsLoadingBalance] = useState(false)
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const mobileMenuRef = useRef<HTMLDivElement>(null)

  // Fetch Solera RA token balance
  useEffect(() => {
    const fetchTokenBalance = async () => {
      if (!connected || !publicKey) {
        setTokenBalance(null)
        return
      }

      setIsLoadingBalance(true)
      try {
        const tokenMint = new PublicKey(SOLERA_RA_TOKEN_ADDRESS)
        const associatedTokenAddress = await getAssociatedTokenAddress(
          tokenMint,
          publicKey
        )

        const tokenAccount = await getAccount(connection, associatedTokenAddress)
        const balance = Number(tokenAccount.amount) / Math.pow(10, 6) // Assuming 6 decimals for RA token
        setTokenBalance(balance)
      } catch (error) {
        console.error('Error fetching token balance:', error)
        setTokenBalance(0) // Default to 0 if account doesn't exist or error occurs
      } finally {
        setIsLoadingBalance(false)
      }
    }

    fetchTokenBalance()
  }, [connected, publicKey, connection])

  // Click outside handler for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsProfileDropdownOpen(false)
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Format token balance for display
  const formatBalance = (balance: number | null): string => {
    if (balance === null) return '0.00'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(balance)
  }

  // Format wallet address for display
  const formatWalletAddress = (address: string): string => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  // Handle wallet disconnect
  const handleDisconnect = async () => {
    try {
      await disconnect()
      setIsProfileDropdownOpen(false)
      setTokenBalance(null)
    } catch (error) {
      console.error('Error disconnecting wallet:', error)
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50">
      <div className="px-6 py-3.5 flex items-center justify-between" style={{ background: 'var(--bg-primary)' }}>
        {/* Left side - Logo and Navigation */}
        <div className="flex items-center space-x-6">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <Image
              src="/logos/solera-full-logo-white.svg"
              alt="Solera"
              width={120}
              height={28}
              className="h-7 w-auto hover:opacity-80 transition-opacity"
              priority
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => {
              const isActive = pathname === item.href && !item.external

              if (item.external) {
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs font-medium text-gray-400 hover:text-gray-300 transition-colors"
                  >
                    {item.name}
                  </a>
                )
              }

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`text-xs font-medium transition-colors relative ${
                    isActive
                      ? 'text-white'
                      : 'text-gray-400 hover:text-gray-300'
                  }`}
                >
                  {item.name}
                  {isActive && (
                    <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-white"></div>
                  )}
                </Link>
              )
            })}
          </nav>
        </div>

        {/* Right side - Controls */}
        <div className="flex items-center space-x-3">
          {/* USD Currency - Hidden on mobile */}
          <span className="hidden sm:block text-xs text-gray-300 font-medium">USD</span>

          {connected ? (
            /* Connected State: Token Balance + Profile Avatar */
            <div className="flex items-center space-x-3">
              {/* Token Balance - Hidden on mobile */}
              <div className="hidden sm:block text-xs text-gray-300 font-medium">
                {isLoadingBalance ? (
                  <span className="animate-pulse">Loading...</span>
                ) : (
                  <span>{formatBalance(tokenBalance)} RA</span>
                )}
              </div>

              {/* Profile Avatar with Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <button
                  onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
                  className="w-7 h-7 flex items-center justify-center cursor-pointer transition-all
                    md:bg-gradient-to-br md:from-cyan-500 md:to-blue-600 md:hover:scale-105 md:transition-transform
                    bg-white hover:bg-gray-100 rounded md:rounded-full"
                >
                  <User className="w-3.5 h-3.5 text-black md:text-white" />
                </button>

                {/* Profile Dropdown */}
                {isProfileDropdownOpen && (
                  <div className="dropdown-menu absolute right-0 top-10 w-64 py-2 z-50">
                    {/* Account Balance Section */}
                    <div className="px-4 py-3 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Account Balance</span>
                        <button
                          onClick={handleDisconnect}
                          className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          Disconnect
                        </button>
                      </div>
                      <div className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>$0.00</div>
                    </div>

                    {/* Solera Token Balance */}
                    <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                      <div className="flex items-center justify-between">
                        <span className="text-sm" style={{ color: 'var(--text-primary)' }}>Solera</span>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm" style={{ color: 'var(--text-primary)' }}>RA {formatBalance(tokenBalance)}</span>
                          <div className="w-2 h-2 bg-blue-500"></div>
                        </div>
                      </div>
                    </div>

                    {/* Wallet Address */}
                    {publicKey && (
                      <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                        <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Wallet</div>
                        <div className="text-sm font-mono" style={{ color: 'var(--text-primary)' }}>
                          {formatWalletAddress(publicKey.toString())}
                        </div>
                      </div>
                    )}

                    {/* Menu Items */}
                    <div className="py-1">
                      <Link
                        href="/"
                        className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                        style={{ color: 'var(--text-primary)' }}
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        Dashboard
                      </Link>
                      <Link
                        href="/deposits"
                        className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                        style={{ color: 'var(--text-primary)' }}
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        Deposits
                      </Link>
                      <Link
                        href="/withdrawals"
                        className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                        style={{ color: 'var(--text-primary)' }}
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        Withdrawals
                      </Link>
                      <Link
                        href="/history"
                        className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                        style={{ color: 'var(--text-primary)' }}
                        onClick={() => setIsProfileDropdownOpen(false)}
                      >
                        Transaction History
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            /* Disconnected State: Connect Wallet Button */
            <WalletMultiButton>
              Connect Wallet
            </WalletMultiButton>
          )}

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="md:hidden w-7 h-7 bg-white rounded flex items-center justify-center hover:bg-gray-100 transition-colors"
          >
            {isMobileMenuOpen ? (
              <X className="w-4 h-4 text-black" />
            ) : (
              <Menu className="w-4 h-4 text-black" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          className="dropdown-menu md:hidden absolute top-16 left-4 right-4 py-2 z-40 rounded"
        >
          {/* Mobile Navigation */}
          <div className="py-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href && !item.external

              if (item.external) {
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-secondary)' }}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                )
              }

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800 ${
                    isActive ? 'bg-gray-800' : ''
                  }`}
                  style={{
                    color: isActive ? 'var(--text-primary)' : 'var(--text-secondary)'
                  }}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              )
            })}
          </div>

          {/* Mobile Token Balance (if connected) */}
          {connected && (
            <div className="border-t px-4 py-3" style={{ borderColor: 'var(--border-primary)' }}>
              <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Token Balance</div>
              <div className="text-sm" style={{ color: 'var(--text-primary)' }}>
                {isLoadingBalance ? (
                  <span className="animate-pulse">Loading...</span>
                ) : (
                  <span>{formatBalance(tokenBalance)} RA</span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </header>
  )
}
