{"buildCommand": "npm run build:vercel", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"SKIP_ENV_VALIDATION": "true", "NODE_ENV": "production"}, "build": {"env": {"SKIP_ENV_VALIDATION": "true", "NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"app/api/**/*.ts": {"runtime": "nodejs18.x", "maxDuration": 10}}, "regions": ["iad1"], "github": {"silent": true}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}