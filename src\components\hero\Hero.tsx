'use client'

import { useState } from 'react'
import { formatCurrency } from '@/lib/utils'
import { ChevronDown, ArrowDownToLine, ArrowUpFromLine } from 'lucide-react'
import Image from 'next/image'
import { BuyRAModal } from '@/components/modals/BuyRAModal'

export function Hero() {
  // Modal state
  const [isBuyModalOpen, setIsBuyModalOpen] = useState(false)

  // Mock data - in real app, this would come from API/database
  const stats = {
    totalStaking: 40899.12,
    freeEquity: 2390.44,
  }

  return (
    <div className="text-center py-6 relative">
      {/* Action Buttons - Top Right */}
      <div className="absolute top-0 right-0 flex items-center space-x-3">
        <button
          className="w-12 h-12 rounded-full bg-gray-800/50 border border-gray-700 flex items-center justify-center hover:bg-gray-700/50 transition-colors group"
          title="Deposit"
        >
          <ArrowDownToLine className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
        </button>
        <button
          className="w-12 h-12 rounded-full bg-gray-800/50 border border-gray-700 flex items-center justify-center hover:bg-gray-700/50 transition-colors group"
          title="Withdraw"
        >
          <ArrowUpFromLine className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
        </button>
      </div>

      {/* Centered Stats */}
      <div className="flex items-center justify-center space-x-16 mb-8 pt-8">
        {/* Total in Staking */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-sm text-gray-400">Total in Staking</span>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-4xl font-bold text-white mb-1">
            {formatCurrency(stats.totalStaking)}
          </div>
          <div className="text-sm text-gray-500">0.0000098 BTC</div>
        </div>

        {/* Free Equity */}
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <span className="text-sm text-gray-400">Free Equity</span>
            <ChevronDown className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-4xl font-bold text-white mb-1">
            {formatCurrency(stats.freeEquity)}
          </div>
          <div className="text-sm text-gray-500">0.0000098 BTC</div>
        </div>
      </div>

      {/* Integrated Wallet Banner */}
      <div className="flex items-center justify-center">
        <div className="bg-gradient-to-r from-cyan-400 via-blue-500 to-teal-500 rounded-[4px] px-3 py-1 flex items-center space-x-5 shadow-xl backdrop-blur-sm">
          {/* Platform Logos - Overlapping */}
          <div className="flex items-center">
            {/* DEXTools Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-40 hover:z-50">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/dextool.png"
                  alt="DEXTools"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* DEXScreener Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-30 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/dexscreener.png"
                  alt="DEXScreener"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* Meteora Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-20 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/meteora.png"
                  alt="Meteora"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>

            {/* Raydium Logo */}
            <div className="w-10 h-10 rounded-full bg-white/15 backdrop-blur-md flex items-center justify-center border border-white/30 hover:bg-white/25 transition-all duration-300 shadow-lg relative z-10 hover:z-50 -ml-5">
              <div className="w-7 h-7 rounded-full overflow-hidden flex items-center justify-center bg-white/90">
                <Image
                  src="/buy/raydium.png"
                  alt="Raydium"
                  width={28}
                  height={28}
                  className="w-full h-full object-contain p-0.5"
                />
              </div>
            </div>
          </div>

          <div className="text-white text-left">
            <span className="text-base font-medium tracking-normal text-ellipsis overflow-hidden">Manage your stakings assets at one place</span>
          </div>

          <button
            onClick={() => setIsBuyModalOpen(true)}
            className="bg-white text-black rounded-[4px] text-sm font-semibold px-3 py-1 transition-all duration-300 shadow-lg border-0 min-h-0"
          >
            Buy
          </button>
        </div>
      </div>

      {/* Buy RA Modal */}
      <BuyRAModal
        isOpen={isBuyModalOpen}
        onClose={() => setIsBuyModalOpen(false)}
      />
    </div>
  )
}
